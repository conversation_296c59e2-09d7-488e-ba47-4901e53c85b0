import { getAllDateRange } from '@/utils/all-date-range.js'
import {
    dictsMonth,
    dictsMonthTotal,
    dictsQuarter
  } from '@/utils/common/dicts.js'

  /**
   * @description 定义日期选择器的选项
   * @param params 数据源参数
   * @param toggleSearch 搜索按钮的函数
   * @param yearRange 是否是年份范围
   * @param year 年份参数名有的日期取值为year，有的为date
   */
export default function (params, toggleSearch, yearRange=false, year='year') {
  const dateRange = reactive({
    maxYear: '', // 最大年份
    minYear: '', // 最小年份
    minMonth: '', // 最小月份
    maxMonth: '' // 最大月份
  })
  /**
   * @description 初始化数据年份、月份
   * @param val 数据源参数
   * @param first 是否是第一次加载
   */
  const initDateRange = (val, first = false) => {
    
    return new Promise(async resolve => {
      try {
        const { minYear, maxYear, minMonth, maxMonth } = await getAllDateRange(val)
        dateRange.maxYear = String(maxYear)
        dateRange.minYear = String(minYear)
        dateRange.minMonth = String(minMonth)
        dateRange.maxMonth = String(maxMonth)
        // console.log(
        //   '是否是日期范围类型', yearRange
        // )
        if (yearRange) {
          // 如果是年份范围，设置年份范围为当前年份和当前年份减4年之间的最大值
          const min = (dateRange.maxYear - 4) > dateRange.minYear ? (dateRange.maxYear - 4) : dateRange.minYear
          // console.log('min', min)
          params[year] = [String(min) , dateRange.maxYear]
        } else {
          params[year] = dateRange.maxYear
        }
        // console.log(
        // `${val}、月份: maxYear=${dateRange.maxYear}, minYear=${dateRange.minYear}, minMonth=${dateRange.minMonth}, maxMonth=${dateRange.maxMonth}`
        // )
        await innerdate()
        if (first) {
          toggleSearch()
        }
        resolve()
      } catch (error) {
        console.error('初始化日期范围时出错:', error)
        resolve()
      }
    })
  }
  // 定义日期选择器的选项
  const disabledFeatureDate = time => {
    // 获取当前日期的年份
    const year = time.getFullYear()
    // 禁用小于 minYear 年和大于 maxYear 年的日期
    return year < dateRange.minYear || year > dateRange.maxYear
  }

  // 定义新的字典数组
  const [newDictsMonthTotal, newDictsQuarter, newDictsMonth] = [ref([]), ref([]), ref([])]
  // 根据年份对应变化月份
  function innerdate() {
    // console.log('指标类型', params.pointerType)
    const currentYear = yearRange ? params[year][1] : params[year]
    return new Promise((resolve, reject) => {
      // 定义一个辅助函数来处理切片逻辑
      const handleSlice = (dict, maxIndex, refVar, pointerType) => {
        if (currentYear === dateRange.maxYear) {
          const index = parseInt(maxIndex, 10)
          refVar.value = dict.slice(0, index - pointerType)
    
          params['month'] = String(maxIndex)
        } else {
          refVar.value = dict
          params['month'] = '12'
        }
      }

      // 指标类型(0-月，2-月累，1-季度)
      switch (params.pointerType) {
        case '1':
          const quarter = String(getCurrentQuarter(currentYear))
          const quarterIndex = Math.max(parseInt(quarter, 10), 1)
          if (currentYear === dateRange.maxYear) {
            newDictsQuarter.value = dictsQuarter.slice(0, quarterIndex)
            params.quarter = String(quarterIndex)
          } else {
            newDictsQuarter.value = dictsQuarter
            params.quarter = '4'
          }
          // console.log('季度', newDictsQuarter)
          break
        case '2':
          const monthTotalIndex = dateRange.maxMonth === '1' ? 2 : dateRange.maxMonth
          // console.log('月份', dictsMonth,monthTotalIndex, newDictsMonth)

          handleSlice(dictsMonthTotal, monthTotalIndex, newDictsMonthTotal, 1)
          break
        default:
          handleSlice(dictsMonth, dateRange.maxMonth, newDictsMonth, 0)
          break
      }
      resolve()
    })
  }
  function getCurrentQuarter(currentYear) {
    if (currentYear === String(new Date().getFullYear())) {
      return Math.ceil(params.month / 3)
    } else {
      return 4
    }
  }
  return {
    initDateRange,
    innerdate,
    disabledFeatureDate,
    newDictsMonthTotal,
    newDictsQuarter,
    newDictsMonth,
    dateRange // 返回 dateRange 以便在重置时访问 maxYear
  }
}
