# 搜索表单重置功能使用指南

这个工具包提供了三种方式来为现有的搜索表单添加重置功能，从简单到复杂，满足不同的使用场景。

## 方案一：简单重置 Hook (useSearchReset)

最简单的使用方式，适合大多数场景。

### 基本使用

```javascript
// 在现有的搜索表单组件中
import useSearchReset from '@/utils/hooks/useSearchReset'

// 现有代码
const params = reactive({
  year: '2024',
  dataSource: '1',
  province: ''
})

function toggleSearch() {
  // 现有的搜索逻辑
  emit('change', params)
}

// 添加重置功能 - 只需要这一行！
const { resetSearch } = useSearchReset(params, toggleSearch)
```

### 在模板中添加重置按钮

```html
<!-- 在现有的查询按钮旁边添加 -->
<el-button type="primary" @click="toggleSearch">查询</el-button>
<el-button icon="Refresh" @click="resetSearch">重置</el-button>
```

### 高级配置

```javascript
const { resetSearch, updateInitialValues } = useSearchReset(params, toggleSearch, {
  // 自定义初始值
  initialValues: {
    year: '2023',
    dataSource: '2',
    province: ''
  },
  // 重置前回调
  beforeReset: () => {
    console.log('准备重置')
  },
  // 重置后回调
  afterReset: () => {
    console.log('重置完成')
  },
  // 是否自动搜索
  autoSearch: true
})
```

## 方案二：使用重置按钮组件 (SearchResetButton)

提供了一个可复用的重置按钮组件。

### 基本使用

```html
<template>
  <el-form>
    <!-- 现有的表单项 -->
    <el-form-item>
      <el-button type="primary" @click="toggleSearch">查询</el-button>
      <SearchResetButton @reset="handleReset" />
    </el-form-item>
  </el-form>
</template>

<script setup>
import SearchResetButton from '@/components/SearchResetButton/index.vue'
import useSearchReset from '@/utils/hooks/useSearchReset'

const { resetSearch } = useSearchReset(params, toggleSearch)

const handleReset = () => {
  resetSearch()
}
</script>
```

### 自定义按钮样式

```html
<SearchResetButton 
  text="清空条件"
  icon="Delete"
  type="danger"
  :show-loading="true"
  @reset="handleReset"
/>
```

## 方案三：完整重置解决方案 (useSearchFormReset)

适合复杂的搜索表单，支持高级重置策略。

### 基本使用

```javascript
import useSearchFormReset from '@/utils/hooks/useSearchFormReset'

const { resetSearch } = useSearchFormReset({
  params,
  searchCallback: toggleSearch,
  resetStrategy: {
    autoSearch: true,
    preserveFields: ['dataSource'], // 保留数据源字段
    resetToEmpty: ['province'],     // 省份重置为空
    resetToDefault: {               // 特定字段重置为默认值
      year: '2024'
    }
  }
})
```

### 自定义重置逻辑

```javascript
const { resetSearch } = useSearchFormReset({
  params,
  searchCallback: toggleSearch,
  customResetLogic: async (params, initialValues) => {
    // 自定义重置逻辑
    params.year = new Date().getFullYear().toString()
    params.province = ''
    params.dataSource = initialValues.dataSource
    
    // 可以调用其他方法
    await someCustomMethod()
  }
})
```

## 实际应用示例

### 示例1：为现有的 SearchFormResource 组件添加重置功能

```javascript
// 在现有组件中，只需要添加几行代码

// 1. 导入 Hook
import useSearchReset from '@/utils/hooks/useSearchReset'

// 2. 在现有的 toggleSearch 函数后添加
const { resetSearch } = useSearchReset(params, toggleSearch)

// 3. 在模板中添加按钮（已完成）
```

### 示例2：为复杂表单添加重置功能

```javascript
// 对于有特殊需求的表单
const { resetSearch, resetField } = useSearchFormReset({
  params,
  searchCallback: toggleSearch,
  resetStrategy: {
    preserveFields: ['segment', 'dataSource'], // 保留板块和数据源
    resetToEmpty: ['province', 'city'],        // 地区信息清空
    resetToDefault: {                          // 时间相关重置为默认值
      year: (new Date().getFullYear() - 1).toString(),
      pointerType: '2'
    }
  }
})

// 也可以重置单个字段
const resetProvince = () => resetField('province')
```

## 迁移现有组件

### 步骤1：识别现有组件结构
- 找到搜索参数对象（通常是 `params` 或 `queryParams`）
- 找到搜索函数（通常是 `toggleSearch` 或 `handleQuery`）

### 步骤2：添加重置功能
```javascript
// 添加导入
import useSearchReset from '@/utils/hooks/useSearchReset'

// 在现有代码后添加
const { resetSearch } = useSearchReset(params, toggleSearch)
```

### 步骤3：添加重置按钮
```html
<!-- 在查询按钮旁边添加 -->
<el-button icon="Refresh" @click="resetSearch">重置</el-button>
```

## 注意事项

1. **参数对象必须是响应式的**：确保使用 `reactive()` 或 `ref()` 创建
2. **初始值保存时机**：Hook 会在创建时保存当前参数值作为初始值
3. **搜索回调**：确保传入的搜索函数能正确触发数据更新
4. **异步操作**：重置和搜索都支持异步操作

## 兼容性

- ✅ Vue 3 Composition API
- ✅ Element Plus
- ✅ 现有的搜索表单组件
- ✅ 支持 TypeScript（需要添加类型定义）

这个解决方案的优势：
- **最小侵入**：只需要添加1-3行代码
- **高度可配置**：支持各种重置策略
- **向后兼容**：不影响现有功能
- **可复用**：一次开发，到处使用
