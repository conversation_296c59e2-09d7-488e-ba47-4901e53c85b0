<template>
  <el-form :model="params" ref="queryRef" label-width="0" :inline="true" class="search-form">
    <el-row :gutter="16">
      <el-col :xs="8" :sm="8" :md="3">
        <el-form-item prop="year">
          <el-date-picker
            v-model="params.year"
            type="year"
            value-format="YYYY"
            format="YYYY"
            :disabled-date="disabledFeatureDate"
            placeholder="年份"
            style="width: 100%"
            :clearable="false"
          />
        </el-form-item>
      </el-col>
      <el-col :xs="8" :sm="8" :md="3">
        <el-form-item prop="pointerType">
          <el-select v-model="params.pointerType" placeholder="指标类型" style="width: 100%">
            <el-option
              v-for="item in dictsPointerType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :xs="8" :sm="8" :md="3">
        <!-- TODO: 指标类型字典字典变换需要注意修改 -->
        <el-form-item v-if="params.pointerType === '2'" prop="month">
          <el-select v-model="params.month" placeholder="月累" style="width: 100%">
            <el-option
              v-for="item in newDictsMonthTotal"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-else-if="params.pointerType === '1'" prop="quarter">
          <el-select v-model="params.quarter" placeholder="季度" style="width: 100%">
            <el-option
              v-for="item in newDictsQuarter"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-else prop="month">
          <el-select v-model="params.month" placeholder="月度" style="width: 100%">
            <el-option
              v-for="item in newDictsMonth"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <DictsResource
        :form="params"
        :dicts="data.linkageData"
        :props="[
          {
            name: '数据来源',
            key: 'dataSource',
            disabled: true,
            // hide: true,
            clearable: true
          },
          {
            name: '板块',
            key: 'segment'
          },
          {
            name: '细分市场',
            key: 'subMarket'
          }
        ]"
        :propsManuFacturer="{ name: '主机厂', key: 'manuFacturer', show: false }"
        :propsEngineFactory="{ name: '发动机厂', key: 'engineFactory', show: false }"
        :propsFuelType="{ name: '燃料', key: 'fuelType', show: false, type: 'B' }"
        :xs="8"
        :sm="8"
        :md="4"
      />
      <el-col :xs="8" :sm="8" :md="3">
        <el-form-item prop="model">
          <el-select-v2
            v-model="params.model"
            :options="data.engineModelData"
            placeholder="发动机型号"
            style="width: 100%"
            filterable
            clearable
          />
          <!-- <el-select v-model="params.model" placeholder="发动机型号" style="width: 100%">
            <el-option
              v-for="item in data.engineModelData"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select> -->
        </el-form-item>
      </el-col>
      <el-col :span="3">
        <el-form-item>
          <el-button type="primary" @click="toggleSearch">查询</el-button>
          <el-button icon="Refresh" @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import DictsResource from '@/views/components/DictsResource.vue'

import { dictsPointerType } from '@/utils/common/dicts.js'
// import { getAllDateRange } from '@/utils/all-date-range.js'
// import formValidate from '@/utils/hooks/formValidate.js'
import useInnerData from '@/utils/hooks/innerData.js'
import useSearchReset from '@/utils/hooks/useSearchReset.js'
const { proxy } = getCurrentInstance()
const { sys_engine_model } = proxy.useDict('sys_engine_model')

const store = useStore()
const props = defineProps({
  params: {
    type: Object,
    required: true,
    default: () => ({
      year: '', // 年份
      month: '', // 月
      pointerType: '', // 指标类型
      quarter: '', // 季度
      id: '10', // top10
      dataSource: '4', // 数据来源
      segment: '', // 板块
      subMarket: '', // 细分市场
      model: ''
    })
  }
})
const emit = defineEmits(['change'])
const data = reactive({
  linkageData: [], // 多级联动数据
  engineModelData: []
})
const params = reactive({ ...toRaw(props.params) })
// 使用自定义 Hook 并传入 params 和 toggleSearch
const {
  initDateRange,
  innerdate,
  disabledFeatureDate,
  newDictsMonthTotal,
  newDictsQuarter,
  newDictsMonth
} = useInnerData(params, toggleSearch)

// 监听年份变化
watch(
  () => params.year,
  val => {
    innerdate()
  }
)
watch(
  () => params.pointerType,
  val => {
    innerdate()
  }
)
data.engineModelData = sys_engine_model

// 添加重置功能 - 只需要这一行！
const { resetSearch } = useSearchReset(params, toggleSearch)

/**
 * @description 点击查询按钮获取参数，并触发emit事件传递搜索参数
 */
function toggleSearch() {
  const data = toRaw(params)
  emit('change', data)
}
const getDictsData = async () => {
  const dicts = await store
    .dispatch('dicts/getDictsData', {
      keyArray: ['dataSource', 'segment', 'subMarket1', 'subMarket2'],
      dataSource: ['中内协']
    })
    .catch(e => e)
  if (dicts && dicts.length > 0) {
    data.linkageData = dicts
  }
}
// 首次加载请求，处理好数据后发起
initDateRange('中内协', true)
getDictsData()
</script>
