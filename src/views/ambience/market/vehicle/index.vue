<template>
  <CommonTabs title="商用数据">
    <template #searchArea>
      <SearchFormResource :params="originParams" @change="getParams" />
    </template>

    <el-row :gutter="20" style="margin-left: 0; margin-right: 0">
      <el-col :xs="24" :sm="24" :md="8">
        <bar v-loading="data.chartA.loading" titleIcon="data4" :title="data.chartA.title" :series="data.chartA.data"
          y-axis-name="单位：(辆)" tooltip-units="辆" :grid="{}" :legend="{
            orient: 'vertical',
            bottom: 4,
            right: 4,
            data: data?.params.subMarket == '客车' ? ['公路', '公交', '校车', '其他轻型客车'] : ''
          }" :xAxis="{ nameTextStyle: { height: '220px' } }" height="270px" :precision="0" show-total reverse-legend
          :tooltip="{ position: positionLeft }" />
      </el-col>
      <el-col :xs="24" :sm="24" :md="16">
        <bar v-loading="data.chartB.loading" titleIcon="data4" :title="data.chartB.title" :series="data.chartB.data"
          :tooltip="{ formatter: formatterB }" y-axis-name="单位：(辆)" :otherYAxis="[{ ...yAxisRight[0], name: '同比：(%)' }]"
          :grid="{}" :legend="{
            bottom: 4,
            right: 4,
            data:
              data?.params.subMarket == '客车'
                ? ['公路', '公交', '校车', '其他轻型客车', '客车同比']
                : ''
          }" height="270px" reverse-legend :precision="0" show-total />
      </el-col>
      <el-col :xs="24" :sm="24" :md="24">
        <bar v-loading="data.chartC.loading" titleIcon="data4" :title="data.chartC.title" :series="data.chartC.data"
          y-axis-name="单位：(%)" yAxisLabelFormate="{value}%" :y-axis-max="100" :tooltip="{ formatter: formatterAll }"
          :grid="{}" :legend="{ orient: 'vertical', bottom: 4, right: 4, data: data.chartC.legendData }"
          :xAxis="{ nameTextStyle: { height: '220px' } }" height="270px" :totalSortLegend="false" reverse-legend />
      </el-col>
      <el-col :xs="24" :sm="24" :md="24">
        <bar v-loading="data.chartD.loading" titleIcon="data4" :title="data.chartD.title" :series="data.chartD.data"
          y-axis-name="单位：(%)" yAxisLabelFormate="{value}%" :y-axis-max="100"
          :tooltip="{ formatter: e => formatterAll(e, false) }" :grid="{}"
          :legend="{ orient: 'vertical', bottom: 4, right: 8 }" :xAxis="{ nameTextStyle: { height: '220px' } }"
          height="270px" :totalSortLegend="false" :legend-wrap="false" reverse-legend />
      </el-col>
      <el-col :xs="24" :sm="24" :md="24">
        <bar v-loading="data.chartE.loading" titleIcon="data4" :title="data.chartE.title" :series="data.chartE.data"
          y-axis-name="单位：(%)" yAxisLabelFormate="{value}%" :y-axis-max="100"
          :tooltip="{ formatter: e => formatterAll(e, false) }" :grid="{}"
          :legend="{ orient: 'vertical', bottom: 4, right: 4 }" :xAxis="{ nameTextStyle: { height: '220px' } }"
          height="270px" :totalSortLegend="true" :legend-wrap="false" reverse-legend />
      </el-col>
      <el-col :xs="24" :sm="24" :md="24">
        <bar v-loading="data.chartF.loading" titleIcon="data4" :title="data.chartF.title" :series="data.chartF.data"
          y-axis-name="单位：(%)" yAxisLabelFormate="{value}%" :y-axis-max="100" :tooltip="{ formatter: formatterAll }"
          :grid="{}" :legend="data.chartF.legend" :xAxis="{ nameTextStyle: { height: '220px' } }" height="270px" />
      </el-col>
      <el-col :xs="24" :sm="24" :md="24" v-if="originParams.subMarket == '客车'">
        <bar v-loading="data.chartG.loading" titleIcon="data4" :title="data.chartG.title" :series="data.chartG.data"
          y-axis-name="单位：(%)" yAxisLabelFormate="{value}%" :y-axis-max="100" :tooltip="{ formatter: formatterAll }"
          :grid="{}" :legend="{ orient: 'vertical', bottom: 4, right: 4 }"
          :xAxis="{ nameTextStyle: { height: '220px' } }" height="270px" :totalSortLegend="false" reverse-legend />
      </el-col>
      <el-col :xs="24" :sm="24" :md="8">
        <bar v-loading="data.chartH.loading" titleIcon="data4" :title="data.chartH.title" :series="data.chartH.data"
          y-axis-name="单位：(%)" yAxisLabelFormate="{value}%" :y-axis-max="100" tooltip-units="%" :grid="{}"
          :legend="{ orient: 'vertical', bottom: 4, right: 4 }" :xAxis="{ nameTextStyle: { height: '220px' } }"
          height="270px" :totalSortLegend="true" reverse-legend :tooltip="{
            position: positionLeft,
            formatter: params =>
              TooltipFormatter(TooltipComponent2, params, {
                mapping: {
                  sales: 'value',
                  proportion: 'slice',
                  yoy: 'slice'
                },
                shouldSort: true,
                sortField: 'value'
              })
          }" />
      </el-col>
      <el-col :xs="24" :sm="24" :md="16">
        <bar v-loading="data.chartI.loading" titleIcon="data4" :title="data.chartI.title" :series="data.chartI.data"
          y-axis-name="单位：(台)" tooltip-units="台" titleRotate :otherYAxis="[{ ...yAxisRight[0], name: '同比：(%)' }]"
          :grid="{ bottom: 60 }" :legend="{ orient: 'vertical', bottom: 4, right: 4 }"
          :xAxis="{ nameTextStyle: { height: '220px' } }" height="270px" :tooltip="{
            position: positionLeft,
            formatter: params =>
              TooltipFormatter(TooltipComponent1, params, {
                mapping: {
                  sales: 'value'
                },
                shouldSort: true,
                singleColumn: true,
                sortField: 'value'
              })
          }" :totalSortLegend="false" reverse-legend />
      </el-col>
      <el-col :xs="24" :sm="24" :md="8">
        <bar v-loading="data.chartJ.loading" titleIcon="data4" :title="data.chartJ.title" :series="data.chartJ.data"
          y-axis-name="单位：(%)" yAxisLabelFormate="{value}%" :y-axis-max="100" tooltip-units="%" :tooltip="{
            position: positionLeft,
            formatter: params =>
              TooltipFormatter(TooltipComponent2, params, {
                mapping: {
                  sales: 'value',
                  proportion: 'slice',
                  yoy: 'slice'
                },
                shouldSort: true,
                sortField: 'value'
              })
          }" :otherYAxis="[{ ...yAxisRight[0], name: '同比：(%)' }]" :grid="{}"
          :legend="{ orient: 'vertical', bottom: 4, right: 4 }" :xAxis="{ nameTextStyle: { height: '220px' } }"
          height="270px" :totalSortLegend="true" reverse-legend />
      </el-col>
      <el-col :xs="24" :sm="24" :md="16">
        <bar v-loading="data.chartK.loading" titleIcon="data4" :title="data.chartK.title" :series="data.chartK.data"
          y-axis-name="单位：(台)" tooltip-units="台" titleRotate :otherYAxis="[{ ...yAxisRight[0], name: '同比：(%)' }]"
          :grid="{ bottom: 60 }" :legend="{ orient: 'vertical', bottom: 4, right: 4 }"
          :xAxis="{ nameTextStyle: { height: '220px' } }" height="270px" :totalSortLegend="true" reverse-legend />
      </el-col>
      <el-col :xs="24" :sm="24" :md="24">
        <bar v-loading="data.chartL.loading" titleIcon="data4" :title="data.chartL.title" :series="data.chartL.data"
          y-axis-name="单位：(台)" tooltip-units="台" titleRotate :otherYAxis="[{ ...yAxisRight[0], name: '占比：(%)' }]"
          :grid="{}" :legend="{ orient: 'vertical', bottom: 4, right: 4 }"
          :xAxis="{ nameTextStyle: { height: '220px' } }" height="270px" :totalSortLegend="true" reverse-legend />
      </el-col>
    </el-row>
  </CommonTabs>
</template>

<script setup lang="jsx">
import CommonTabs from '@/views/components/tabs/CommonTabs'
import SearchFormResource from './components/SearchFormResource.vue'
import bar from '@/views/components/echarts/bar.vue'

import calChartsData from '@/utils/hooks/calChartsData.js'
import { positionLeft } from '@/utils/echarts'
import provinceFull2Jian from '@/utils/common/map/provinceFull2Jian.json'

import {
  commVehicleSalesYoyList,
  commVehicleSalesByQueryTypeList,
  commVehicleManuFacturerSalesList,
  commVehicleEngineSalesList,
  selectCommVehicleAreaSaleList
} from '@/api/intelligence/marketEnvCommVehicle.js'
import { TooltipFormatter, setYuchaiColor } from '@/utils/common/method.js'
import Tpis from '@/views/components/tooltip/index.vue'
import { useStore } from 'vuex'
import { seriesSortOtherFirst } from '@/utils/common/method'
import { typePointerType } from '@/utils/common/dicts'
import { numberFormat } from '@/utils/format'
import YcCharts from '@/components/YcCharts/index.vue';

const store = useStore()
const dataSource = store.state.dicts.dictsDataSource.find(v => v.label === '上险数')?.value
// 初始化搜索条件
const originParams = {
  pointerType: '0', // 指标类型(0-月，2-月累，1-季度)
  year: (new Date().getFullYear() - 1).toString(), // 年份
  segment: '商用车', // 板块
  dataSource: dataSource, // 数据来源 (1/6)
  province: '', // 省份
  engineFactory: '', // 发动机厂
  manuFacturer: '', // 主机厂
  driveType: '', // 驱动形式(需确认)
  weightMidLight: '', // 车型 重中轻(需确认)
  fuelType: '', // 燃料
  subMarket1: '',
  subMarket: 'all', // all-总体；卡车-卡车；客车-客车；牵引车-牵引车……
  breed: '', // 品系：牵引车、载货车、专用车、自卸车、皮卡。选择总体、卡车、客车时不填，其他必填。
  cylinders: '', // 气缸数
  dataType: []
  // month: '12', // 月
  // quarter: '' // 季度
}
const data = reactive({
  params: { ...originParams },
  chartA: {
    title: '',
    data: [],
    loading: false
  },
  chartB: {
    title: '',
    data: [],
    loading: false
  },
  chartC: {
    title: '',
    data: [],
    loading: false,
    legendData: ['柴油', '气体', '汽油', '新能源（不含柴混、气混）']
  },
  chartD: {
    title: '',
    data: [],
    loading: false
  },
  chartE: {
    title: '',
    data: [],
    loading: false
  },
  chartF: {
    title: '',
    data: [],
    legend: {},
    loading: false
  },
  chartG: {
    title: '',
    data: [],
    loading: false
  },
  chartH: {
    title: '',
    data: [],
    loading: false
  },
  chartI: {
    title: '',
    data: [],
    loading: false
  },
  chartJ: {
    title: '',
    data: [],
    loading: false
  },
  chartK: {
    title: '',
    data: [],
    loading: false
  },
  chartL: {
    title: '',
    data: [],
    loading: false
  }
})

const { yAxisRight, referData, setOneArraySeriesData, sortLegendByArray, sortByArray } =
  calChartsData()
function initData() {
  const params = JSON.parse(JSON.stringify(data.params))
  params.dataType = Array.isArray(params.dataType) ? params.dataType.join() : (params.dataType || '')
  params.province = provinceFull2Jian[params.province]
  if (!params.province) params.province = ''
  if (params.pointerType === '1') params.month = params.quarter
  initDataA(params)
  initDataB0(params)
  initDataB1(params)
  initDataB2(params)
  initDataB3(params)
  initDataB4(params)
  initDataC(params)
  initDataD(params)
  initDataE(params)
}
//
const dataLegendRankKaCheAB = [
  '牵引车',
  '载货车',
  '专用车',
  '自卸车',
  '皮卡',
  '3.5吨以下轻卡及其他'
]


async function initDataA(param) {
  // if (data.chartA.loading || data.chartB.loading) return
  data.chartA.loading = true
  data.chartB.loading = true
  const {
    code,
    data: { subListMap, yearListMap, yoyList }
  } = await commVehicleSalesYoyList(param).catch(e => e)

  if (code !== 200) return (data.chartA.loading = false), (data.chartB.loading = false)
  if (subListMap['@type']) delete subListMap['@type']
  if (yearListMap['@type']) delete yearListMap['@type']
  let barA = []
  for (let i in yearListMap) {
    yearListMap[i].forEach(el => {
      barA.push(el)
    })
  }
  // 堆叠图销量 折线图同比
  barA.forEach(el => {
    el.xAxisName = el.year + '年'
    if (el.yoy) el.yoy = Number(el.slice.replace(/%/g, ''))
    if (el.slice) el.slice = Number(el.slice.replace(/%/g, ''))
  })
  barA = barA.filter(e => {
    if (e.segment && e.segment !== '其他') return e
  })
  let seriesBarA = setOneArraySeriesData({
    list: barA,
    xAxisKey: 'xAxisName',
    yAxisKey: 'sale',
    legendKey: 'segment'
  })
  seriesBarA.forEach(el => {
    el.type = 'bar'
    el.stack = 'barA'
    el.yAxisIndex = 0
    el.barMaxWidth = 30
  })
  if (
    param.subMarket1 === '卡车' &&
    (param.breed === '' || param.breed === null || param.breed === undefined)
  ) {
    // 细分市场1按dataLegendRankKaCheAB排序分类
    //     const dataLegendRankKaCheAB = [
    //   '3.5吨以下轻卡及其他',
    //   '皮卡',
    //   '自卸车',
    //   '专用车',
    //   '载货车',
    //   '牵引车'
    // ]
    seriesBarA = sortLegendByArray(seriesBarA, dataLegendRankKaCheAB)
  }
  // 如果 weightMidLight 为重卡或者中卡就去除3.5吨以下轻卡及其他数据
  if (param.weightMidLight === '重卡' || param.weightMidLight === '中卡') {
    seriesBarA = seriesBarA.filter(e => {
      if (e.name !== '3.5吨以下轻卡及其他') return e
    })
  }
  data.chartA.data = seriesBarA
  data.chartA.title = `${param.subMarket === 'all' ? '商用车总体' : param.subMarket}市场年度销量趋势`
  await nextTick()
  data.chartA.loading = false
  // 右边图表
  let barB = []
  for (let i in subListMap) {
    subListMap[i].forEach(el => {
      barB.push(el)
    })
  }
  barB.forEach(el => {
    // pointerType: '0', // 指标类型(0-月，2-月累，1-季度)
    if (param.pointerType === '1') {
      el.xAxisName = referData.quarterRefer[el.month]
    } else {
      el.month = Number(el.month)
      el.xAxisName = el.month + '月'
    }
    el.slice =
      el.slice === undefined || el.slice === null
        ? ''
        : Number(el.slice.toString().replace(/%/g, ''))
    el.tooltipValue = `占比:${numberFormat(el.slice, 1)}%;销量:${numberFormat(el.sale, 0)}台;同比:${el.yoy ? el.yoy : 0}`
  })
  barB = barB.filter(e => {
    if (e.segment && e.segment !== '其他') return e
  })
  let seriesBarB = setOneArraySeriesData({
    list: barB,
    xAxisKey: 'xAxisName',
    yAxisKey: 'sale',
    legendKey: 'segment'
  })
  seriesBarB.forEach(el => {
    el.type = 'bar'
    el.stack = 'barB'
    el.yAxisIndex = 0
    el.barMaxWidth = 30
  })
  // pointerType: '0', // 指标类型(0-月，2-月累，1-季度)
  seriesBarB = sortByArray(
    seriesBarB,
    param.pointerType === '1' ? referData.quarterSort : referData.monthSort,
    'xAxisName'
  )
  let seriesLineB = []
  yoyList.forEach(el => {
    // pointerType: '0', // 指标类型(0-月，2-月累，1-季度)
    if (param.pointerType === '1') {
      el.xAxisName = referData.quarterRefer[el.month]
    } else {
      el.month = Number(el.month)
      el.xAxisName = el.month + '月'
    }
    el.tooltipValue = el.yoy
    el.yoy =
      el.yoy === undefined || el.yoy === null ? '' : Number(el.yoy.toString().replace(/%/g, ''))
    el.legendName = `${param.subMarket === 'all' ? '商用车' : param.subMarket}同比`
  })
  seriesLineB = setOneArraySeriesData({
    list: yoyList,
    xAxisKey: 'xAxisName',
    yAxisKey: 'yoy',
    legendKey: 'legendName'
  })
  seriesLineB.forEach(el => {
    el.type = 'line'
    el.stack = null
    el.yAxisIndex = 1
  })
  seriesLineB = sortByArray(
    seriesLineB,
    param.pointerType === '1' ? referData.quarterSort : referData.monthSort,
    'xAxisName'
  )
  let seriesB = []
  if (
    param.subMarket1 === '卡车' &&
    (param.breed === '' || param.breed === null || param.breed === undefined)
  ) {
    // 细分市场1按dataLegendRankKaCheAB排序分类
    seriesBarB = sortLegendByArray(seriesBarB, dataLegendRankKaCheAB)
  }
  seriesB.push(...seriesBarB, ...seriesLineB)
  if (param.weightMidLight === '重卡' || param.weightMidLight === '中卡') {
    seriesB = seriesBarB.filter(e => {
      if (!e.name.includes('3.5吨以下轻卡及其他')) return e
    })
  }
  data.chartB.data = seriesB
  data.chartB.title = `${param.subMarket === 'all' ? '商用车总体' : param.subMarket}${param.pointerType === '1' ? '季度' : param.pointerType === '2' ? '月累' : '月度'}市场销量趋势`
  await nextTick()
  data.chartB.loading = false
}
function dealSeriesData(param, listA, listB) {
  if (listA['@type']) delete listA['@type']
  if (listB['@type']) delete listB['@type']
  const dataA = []
  const sortArrayA = []
  for (let i in listA) {
    sortArrayA.push(`${i}年`)
    listA[i].forEach(el => {
      dataA.push(el)
    })
  }
  dataA.forEach(el => {
    el.xAxisName = el.year + '年'
    el.tooltipValue = `${el.slice}，${numberFormat(el.sale, 0)}台`
    if (el.slice) {
      el.slice = Number(el.slice.replace(/%/g, ''))
    } else {
      el.slice = ''
    }
  })
  const dataB = []
  for (let i in listB) {
    listB[i].forEach(el => {
      dataB.push(el)
    })
  }
  dataB.forEach(el => {
    // pointerType: '0', // 指标类型(0-月，2-月累，1-季度)
    el.tooltipValue = `${el.slice}，${numberFormat(el.sale, 0)}台`
    if (param.pointerType === '1') {
      el.xAxisName = referData.quarterRefer[el.month]
    } else {
      el.month = Number(el.month)
      el.xAxisName = el.month + '月'
    }
    if (el.slice) el.slice = Number(el.slice.replace(/%/g, ''))
  })
  let dataArray = [...dataA, ...dataB]
  dataArray = dataArray.filter(e => {
    if (e.segment && e.segment !== '') return e
  })
  let seriesBar = setOneArraySeriesData({
    list: dataArray,
    xAxisKey: 'xAxisName',
    yAxisKey: 'slice',
    legendKey: 'segment'
  })
  // pointerType: '0', // 指标类型(0-月，2-月累，1-季度)
  seriesBar = sortByArray(
    seriesBar,
    param.pointerType === '1'
      ? [...sortArrayA, ...referData.quarterSort]
      : [...sortArrayA, ...referData.monthSort],
    'xAxisName'
  )
  seriesBar.forEach(ele => {
    ele.data.forEach(el => {
      if (el.name === '1月') {
        const newName = param.year + '年' + el.name
        el.name = newName
        el.xAxisName = newName
      }
    })
  })
  return seriesBar
}
async function initDataB0(param) {
  // if (data.chartC.loading) return
  data.chartC.loading = true
  param.queryType = '0'
  const {
    code,
    data: { subListMap, yearListMap }
  } = await commVehicleSalesByQueryTypeList(param)
  if (code !== 200) return (data.chartC.loading = false)
  const series = dealSeriesData(param, yearListMap, subListMap)
  series.forEach(el => {
    if (el.name === '其他') el.name = '新能源（不含柴混、气混）'
  })
  data.chartC.data = series
  data.chartC.title = `${param.subMarket === 'all' ? '商用车' : param.subMarket}市场燃料类型销量趋势`
  await nextTick()
  data.chartC.loading = false
}

async function initDataB1(param) {
  // if (data.chartD.loading) return
  data.chartD.loading = true
  param.queryType = '1'
  const {
    code,
    data: { subListMap, yearListMap }
  } = await commVehicleSalesByQueryTypeList(param)
  if (code !== 200) return (data.chartD.loading = false)
  const series = dealSeriesData(param, yearListMap, subListMap)
  data.chartD.data = sortLegendByNumber(series)
  data.chartD.title = `${param.subMarket === 'all' ? '商用车' : param.subMarket}市场马力段销量趋势`
  await nextTick()
  data.chartD.loading = false
}
async function initDataB2(param) {
  // if (data.chartE.loading) return
  data.chartE.loading = true
  param.queryType = '2'
  const {
    code,
    data: { subListMap, yearListMap }
  } = await commVehicleSalesByQueryTypeList(param)
  if (code !== 200) return (data.chartE.loading = false)
  const series = dealSeriesData(param, yearListMap, subListMap)
  data.chartE.data = sortLegendByNumber(series)
  data.chartE.title = `${param.subMarket === 'all' ? '商用车' : param.subMarket}市场排量段销量趋势`
  await nextTick()
  data.chartE.loading = false
}
async function initDataB3(param) {
  // if (data.chartF.loading) return
  data.chartF.loading = true
  param.queryType = '3'
  const {
    code,
    data: { subListMap, yearListMap }
  } = await commVehicleSalesByQueryTypeList(param)
  if (code !== 200) return (data.chartF.loading = false)
  let series = dealSeriesData(param, yearListMap, subListMap)
  series = sortLegendByNumber(series)
  const seriesLegend = []
  series.forEach(el => {
    if (el.name.indexOf('3') === -1 && el.name.indexOf('5') === -1 && el.name.indexOf('8') === -1)
      seriesLegend.unshift(el)
  })
  data.chartF.legend = { data: seriesLegend }
  // console.log('series',series)
  data.chartF.data = series.map(x => {
    if (x.name == '0缸') {
      x.name = '新能源（不含混动）'
    }
    return x
  })
  data.chartF.title = `${param.subMarket === 'all' ? '商用车' : param.subMarket}市场气缸数销量趋势`
  await nextTick()
  data.chartF.loading = false
}
async function initDataB4(param) {
  // if (data.chartG.loading) return
  data.chartG.loading = true
  param.queryType = '4'
  const {
    code,
    data: { subListMap, yearListMap }
  } = await commVehicleSalesByQueryTypeList(param)
  if (code !== 200) return (data.chartG.loading = false)
  let series = dealSeriesData(param, yearListMap, subListMap)
  series = sortLegendByNumber(series)
  data.chartG.data = series
  data.chartG.title = `${param.subMarket === 'all' ? '商用车' : param.subMarket}驱动形式销量情况`
  await nextTick()
  data.chartG.loading = false
}
async function initDataC(param) {
  // if (data.chartH.loading || data.chartI.loading) return
  data.chartH.loading = true
  data.chartI.loading = true
  const {
    code,
    data: { subListMap: listB, yearListMap: listA }
  } = await commVehicleManuFacturerSalesList(param)
  if (code !== 200) return (data.chartH.loading = false), (data.chartI.loading = false)
  // 左堆积按最新年份企业汇总，往年不在最新年份中的计⼊其他，实例按TOP10+1
  if (listA['@type']) delete listA['@type']
  const dataA = []
  const sortArrayA = []
  for (let i in listA) {
    sortArrayA.push(`${i}年`)
    listA[i].forEach(el => {
      dataA.push(el)
    })
  }
  dataA.forEach(el => {
    el.xAxisName = el.year + '年'
    if (el.slice) {
      el.slice = Number(el.slice.replace(/%/g, ''))
    } else {
      el.slice = ''
    }
  })

  let seriesBarLeft = setOneArraySeriesData({
    list: dataA,
    xAxisKey: 'xAxisName',
    yAxisKey: 'slice',
    legendKey: 'manufacturer'
  })

  const newSeriesBarLeft = []
  let otherItem = null
  seriesBarLeft.forEach(el => {
    if (el.name !== '其他') {
      newSeriesBarLeft.push(el)
    } else {
      otherItem = el
    }
  })
  if (otherItem) newSeriesBarLeft.push(otherItem)
  data.chartH.data = newSeriesBarLeft
  data.chartH.title = `${param.subMarket === 'all' ? '商用车' : param.subMarket}市场年度主机厂销量情况`
  console.log('堆叠图', seriesSortOtherFirst(seriesBarLeft))


  data.chartH.data = seriesSortOtherFirst(seriesBarLeft)
  data.chartH.title = `${param.subMarket === 'all' ? '商用车' : param.subMarket}市场年度主机厂销量情况`
  await nextTick()
  data.chartH.loading = false

  const dataB = listB
  const currentYear = Number(param.year)
  const yearSale = []
  const curSale = []
  const yoy = []
  dataB.forEach(el => {
    el.yoy =
      el.yoy === undefined || el.yoy === null ? '' : Number(el.yoy.toString().replace(/%/g, ''))
    el.yerSale =
      el.yerSale === undefined || el.yerSale === null
        ? ''
        : Number(el.yerSale.toString().replace(/%/g, ''))
    el.curSale =
      el.curSale === undefined || el.curSale === null
        ? ''
        : Number(el.curSale.toString().replace(/%/g, ''))
    curSale.push({ legendName: `${currentYear - 1}年`, yAxisValue: el.yerSale, ...el })
    yearSale.push({ legendName: `${currentYear}年`, yAxisValue: el.curSale, ...el })
    yoy.push({ legendName: `${currentYear}年同比`, yAxisValue: el.yoy, ...el })
  })
  const dataArrayB = [...yearSale, ...curSale]
  let seriesBarB = setOneArraySeriesData({
    list: dataArrayB,
    xAxisKey: 'manufacturer',
    yAxisKey: 'yAxisValue',
    legendKey: 'legendName'
  })
  seriesBarB.forEach(el => {
    el.type = 'bar'
    el.stack = null
    el.yAxisIndex = 0
    el.barMaxWidth = 30
  })
  let seriesLineB = setOneArraySeriesData({
    list: yoy,
    xAxisKey: 'manufacturer',
    yAxisKey: 'yAxisValue',
    legendKey: 'legendName'
  })
  seriesBarB.forEach(el => {
    el.barGap = '0'
  })
  seriesLineB.forEach(el => {
    el.type = 'line'
    el.yAxisIndex = 1
    el.stack = null
  })
  // 数组倒过来
  seriesBarB = seriesBarB.reverse()

  data.chartI.data = [...seriesBarB, ...seriesLineB]
  // data.chartI.title = `${param.subMarket === 'all' ? '商用车' : param.subMarket}市场年度主机厂销量情况`
  data.chartI.title = `${param.subMarket === 'all' ? '商用车' : param.subMarket}市场${typePointerType[param.pointerType]}主机厂销量情况`
  await nextTick()
  data.chartI.loading = false
}

async function initDataD(param) {
  // if (data.chartJ.loading || data.chartK.loading) return
  data.chartJ.loading = true
  data.chartK.loading = true
  const {
    code,
    data: { subListMap: listB, yearListMap: listA }
  } = await commVehicleEngineSalesList(param)
  if (code !== 200) return (data.chartJ.loading = false), (data.chartK.loading = false)
  // 左堆积按最新年份企业汇总，往年不在最新年份中的计⼊其他，实例按TOP10+1
  // console.log('listB', listB)
  if (listA['@type']) delete listA['@type']
  const dataA = []
  const sortArrayA = []
  for (let i in listA) {
    sortArrayA.push(`${i}年`)
    listA[i].forEach(el => {
      dataA.push(el)
    })
  }
  dataA.forEach(el => {
    el.xAxisName = el.year + '年'
    if (el.slice) {
      el.slice = Number(el.slice.replace(/%/g, ''))
    } else {
      el.slice = ''
    }
  })

  let seriesBarLeft = setOneArraySeriesData({
    list: dataA,
    xAxisKey: 'xAxisName',
    yAxisKey: 'slice',
    legendKey: 'manufacturer'
  })
  const newSeriesBarLeft = []
  let otherItem = null
  seriesBarLeft.forEach(el => {
    if (el.name !== '其他') {
      newSeriesBarLeft.push(el)
    } else {
      otherItem = el
    }
  })
  if (otherItem) newSeriesBarLeft.push(otherItem)
  console.log('排序', newSeriesBarLeft)
  data.chartJ.data = newSeriesBarLeft

  data.chartJ.title = `${param.subMarket === 'all' ? '商用车' : param.subMarket}市场年度发动机厂销量情况`
  await nextTick()
  data.chartJ.loading = false

  const dataB = listB
  const currentYear = Number(param.year)
  const yearSale = []
  const curSale = []
  const yoy = []
  dataB.forEach(el => {
    el.yoy =
      el.yoy === undefined || el.yoy === null ? '' : Number(el.yoy.toString().replace(/%/g, ''))
    el.yerSale =
      el.yerSale === undefined || el.yerSale === null
        ? ''
        : Number(el.yerSale.toString().replace(/%/g, ''))
    el.curSale =
      el.curSale === undefined || el.curSale === null
        ? ''
        : Number(el.curSale.toString().replace(/%/g, ''))
    curSale.push({
      legendName: `${currentYear - 1}年`,
      yAxisValue: el.yerSale,
      tooltipValue: `${numberFormat(el.yerSale, 0)}台`,
      ...el
    })
    yearSale.push({
      legendName: `${currentYear}年`,
      yAxisValue: el.curSale,
      tooltipValue: `${numberFormat(el.curSale), 0}台`,
      ...el
    })
    yoy.push({
      legendName: `${currentYear}年同比`,
      yAxisValue: el.yoy,
      tooltipValue: `${el.yoy}%`,
      ...el
    })
  })
  const dataArrayB = [...yearSale, ...curSale]
  let seriesBarB = setOneArraySeriesData({
    list: dataArrayB,
    xAxisKey: 'manufacturer',
    yAxisKey: 'yAxisValue',
    legendKey: 'legendName'
  })
  seriesBarB.forEach(el => {
    el.type = 'bar'
    el.stack = null
    el.yAxisIndex = 0
    el.barMaxWidth = 30
  })
  let seriesLineB = setOneArraySeriesData({
    list: yoy,
    xAxisKey: 'manufacturer',
    yAxisKey: 'yAxisValue',
    legendKey: 'legendName'
  })
  seriesLineB.forEach(el => {
    el.type = 'line'
    el.yAxisIndex = 1
    el.stack = null
  })
  seriesBarB.forEach(el => {
    el.barGap = '0'
  })
  //  console.log('配色',d)
  seriesBarB = seriesBarB.reverse()
  // tooltipValue
  let d = setYuchaiColor(seriesBarB)

  data.chartK.data = [...d, ...seriesLineB]

  // data.chartK.title = `${param.subMarket === 'all' ? '商用车' : param.subMarket}市场年度发动机厂销量情况`
  data.chartK.title = `${param.subMarket === 'all' ? '商用车' : param.subMarket}市场${typePointerType[param.pointerType]}发动机厂销量情况`
  await nextTick()
  data.chartK.loading = false
}
async function initDataE(param) {
  // if (data.chartL.loading) return
  data.chartL.loading = true
  const {
    code,
    data: { subListMap: listB }
  } = await selectCommVehicleAreaSaleList(param)
  if (code !== 200) return (data.chartL.loading = false)
  const dataB = listB
  const currentYear = Number(param.year)
  const yearSale = []
  const curSale = []
  const yuchaiSlice = []
  dataB.forEach(el => {
    el.yuchaiSlice =
      el.yuchaiSlice === undefined || el.yuchaiSlice === null
        ? ''
        : Number(el.yuchaiSlice.toString().replace(/%/g, ''))
    el.yerSale =
      el.yerSale === undefined || el.yerSale === null
        ? ''
        : Number(el.yerSale.toString().replace(/%/g, ''))
    el.curSale =
      el.curSale === undefined || el.curSale === null
        ? ''
        : Number(el.curSale.toString().replace(/%/g, ''))
    curSale.push({
      legendName: `${currentYear - 1}年`,
      yAxisValue: el.yerSale,
      tooltipValue: `${numberFormat(el.yerSale, 0)}辆`,
      ...el
    })
    yearSale.push({
      legendName: `${currentYear}年`,
      yAxisValue: el.curSale,
      tooltipValue: `${numberFormat(el.curSale, 0)}辆`,
      ...el
    })
    yuchaiSlice.push({
      legendName: `${currentYear}年玉柴占比`,
      yAxisValue: el.yuchaiSlice,
      tooltipValue: `${el.yuchaiSlice}%`,
      ...el
    })
  })
  const dataArrayB = [...yearSale, ...curSale]
  let seriesBarB = setOneArraySeriesData({
    list: dataArrayB,
    xAxisKey: 'province',
    yAxisKey: 'yAxisValue',
    legendKey: 'legendName'
  })
  seriesBarB.forEach(el => {
    el.type = 'bar'
    el.stack = null
    el.yAxisIndex = 0
    el.barMaxWidth = 30
  })
  let seriesLineB = setOneArraySeriesData({
    list: yuchaiSlice,
    xAxisKey: 'province',
    yAxisKey: 'yAxisValue',
    legendKey: 'legendName'
  })
  seriesBarB.forEach(el => {
    el.barGap = '0'
  })
  seriesLineB.forEach(el => {
    el.type = 'line'
    el.yAxisIndex = 1
    el.stack = null
  })
  data.chartL.data = [...seriesBarB, ...seriesLineB]
  data.chartL.title = `${param.subMarket === 'all' ? '商用车' : param.subMarket}市场区域销量情况`
  await nextTick()
  data.chartL.loading = false
}
function getParams(ev) {
  const params = JSON.parse(JSON.stringify(ev))
  data.params = params
  initData()
}

function sortLegendByNumber(ev) {
  let list = JSON.parse(JSON.stringify(ev))
  // rankNumberArray 获取name中的数字
  list.forEach(el => {
    if (el.name === undefined || el.name === null) el.name = ''
    el.rankNumberArray = (el.name.match(/\d+/g) || [100000000]).map(Number)
  })
  list.sort((a, b) => {
    if (a.rankNumberArray[0] !== b.rankNumberArray[0]) {
      if (a.rankNumberArray[0] === undefined) a.rankNumberArray[0] = 0
      if (b.rankNumberArray[0] === undefined) b.rankNumberArray[0] = 0
      return a.rankNumberArray[0] - b.rankNumberArray[0]
    } else if (a.rankNumberArray[1] !== b.rankNumberArray[1]) {
      if (a.rankNumberArray[1] === undefined) a.rankNumberArray[1] = 0
      if (b.rankNumberArray[1] === undefined) b.rankNumberArray[1] = 0
      return a.rankNumberArray[1] - b.rankNumberArray[1]
    } else {
      if (a.rankNumberArray[2] === undefined) a.rankNumberArray[2] = 0
      if (b.rankNumberArray[2] === undefined) b.rankNumberArray[2] = 0
      return a.rankNumberArray[2] - b.rankNumberArray[2]
    }
  })
  list.forEach(el => {
    delete el.rankNumberArray
  })
  return list
}

const formatterAll = (params, isSort) => {
  // console.log('params', isSort)
  if (!params) return
  if (params.length > 0 && params.every(el => el.value === '')) return
  if (isSort) {
    params.sort((a, b) => {
      return (b.value || 0) - (a.value || 0)
    })
  } else {
    // 进行排序
    params.reverse()
  }

  let totalNum = 0
  let itemsHtml = ''
  for (let i = 0; i < params.length; i++) {
    // 计算总计
    if (params[i].data.sale && params[i].data.sale > 0) totalNum += params[i].data.sale
    if (params[i].value === '' || params[i].value === null || params[i].value === undefined)
      continue
    itemsHtml += `<div class="charts-tooltip-item">
        <span>${params[i].marker}${params[i].seriesName}&emsp;</span>
        <span>${params[i].data.tooltipValue}</span>
      </div>`
  }

  let tooltipHtml = `
    <div>
      <div> ${params && params[0] ? params[0].name : ''}</div>
      ${itemsHtml}
       <div class="charts-tooltip-item"><span>总计</span><span>100%，${totalNum}台</span></div>
    </div>`
  return tooltipHtml
}

// Tooltip组件渲染
const TooltipComponent1 = ({ params, mapping, shouldSort, singleColumn }) => {
  return (
    <Tpis params={params} mapping={mapping} shouldSort={shouldSort} singleColumn={singleColumn}>
      {{
        item: ({ item }) => {
          return (
            <>
              <span>
                {numberFormat(item.value, item.seriesName.includes('同比') ? 1 : 0) || 0}
                {item.seriesName.includes('同比') ? '%' : '台'}
              </span>
            </>
          )
        }
      }}
    </Tpis>
  )
}
// Tooltip组件渲染
const TooltipComponent2 = ({ params, mapping, shouldSort, singleColumn }) => {
  return (
    <Tpis params={params} mapping={mapping} shouldSort={shouldSort} singleColumn={singleColumn}>
      {{
        item: ({ item }) => {
          return (
            <>
              <span>{item.value || 0}%</span>
            </>
          )
        }
      }}
    </Tpis>
  )
}

const formatterB = (params) => {
  if (!params) return
  let totalNum = 0
  let itemsHtml = ''
  let itemsTb = ''
  for (let i = 0; i < params.length; i++) {
    // 计算总计
    if (params[i].data.sale && params[i].data.sale > 0) totalNum += params[i].data.sale
    if (params[i].value === '' || params[i].value === null || params[i].value === undefined) { continue }
    if (params[i].seriesName.includes('同比')) {
      itemsTb = params[i].data.tooltipValue;
      continue
    }
    if (params[i].seriesName.includes('总计')) { continue }
    itemsHtml += `<div class="charts-tooltip-item">
        <span>${params[i].marker}${params[i].seriesName}&emsp;</span>
        <span>${params[i].data.tooltipValue}</span>
      </div>`
  }
  let tooltipHtml = `
    <div>
      <div> ${params && params[0] ? params[0].name : ''}</div>
      ${itemsHtml}
       <div class="charts-tooltip-item">
         <span>总计&emsp;</span>
         <span>销量:${numberFormat(totalNum, 0)}辆;同比:${itemsTb}</span>
       </div>
    </div>`
  return tooltipHtml
}

</script>

<style lang="scss" scoped>
.search-form {
  :deep(.el-col) {
    margin-bottom: 0;
  }
}

:deep(.el-col) {

  // margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0;
  }
}
</style>
